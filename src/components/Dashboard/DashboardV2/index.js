"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DashboardPromotionCard } from "@/components/Dashboard/DashboardV2/components/PromotionCard";
import { useState, useCallback, useRef, useEffect } from "react";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import UploadDialog from "@/components/Dashboard/UploadDialog";
import YouTubeUploadDialog from "@/components/Dashboard/YouTubeUploadDialog";
import { transcriptionService } from "@/services/api/transcriptionService";
import { RemainingMinutes } from "./components/RemainingMinutes";
import { FileList } from "./components/FileList";
import { useAuthStore } from "@/stores/useAuthStore";
import supabase from "@/lib/supabaseClient";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/navigation";
import UpgradeDialog from "@/components/Dashboard/UpgradeDialog";
import LimitReachedDialog from "@/components/Dashboard/LimitReachedDialog";
import AppSumoWelcomeDialog from "@/components/Dashboard/AppSumoWelcomeDialog";
import AppSumoActivationErrorDialog from "@/components/Dashboard/AppSumoActivationErrorDialog";
import { useDashboardInitialize } from "@/hooks/useDashboardInitialize";

export default function DashboardV2() {
  const t = useTranslations("dashboard");
  const router = useRouter();
  const { fetchEntitlements } = useEntitlementsStore();
  const [showUpload, setShowUpload] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitMessage, setLimitMessage] = useState("");
  const [upgradeSource, setUpgradeSource] = useState("dashboard");
  const [showAppSumoWelcomeDialog, setShowAppSumoWelcomeDialog] = useState(false);
  const youtubeDialogRef = useRef(null);
  const { user, appsumoActivationError, clearAppSumoActivationError } = useAuthStore();
  const shouldShowPromoCard = user && !user.hasActiveSubscription && !user.primaryPlanDetail?.isAppsumo;

  // Define all hooks and callbacks first, before any conditional returns
  const handleUploadSuccess = useCallback(
    async (transcription) => {
      setShowUpload(false);
      try {
        // Create transcription task
        await transcriptionService.doTranscription(transcription.id);
        router.push(`/transcriptions/${transcription.id}`);
      } catch (error) {
        console.error("Failed to create transcription task:", error);
      }
    },
    [router]
  );

  const handleYouTubeSubmitSuccess = useCallback(
    (transcription) => {
      router.push(`/transcriptions/${transcription.id}`);
    },
    [router]
  );

  const onUploadClick = useCallback(() => {
    setShowUpload(true);
    fetchEntitlements();
  }, [fetchEntitlements]);

  const onYouTubeClick = useCallback(() => {
    youtubeDialogRef.current?.openDialog();
    fetchEntitlements();
  }, [fetchEntitlements]);

  useEffect(() => {
    const checkAuth = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session || session?.user?.is_anonymous) {
        router.replace("/auth/signin");
      } else {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [user, router]);

  // 使用共享的 Dashboard 初始化逻辑
  useDashboardInitialize({
    setShowLimitDialog,
    setLimitMessage,
    setUpgradeSource,
    setShowAppSumoWelcomeDialog,
    source: "dashboard",
    isLoading
  });

  // 处理升级按钮点击
  const handleUpgradeClick = () => {
    setShowLimitDialog(false);
    setShowUpgradeDialog(true);
  };

  return (
    <div className="w-full">
      <div className="flex flex-col lg:flex-row gap-4 md:gap-8 h-full min-h-[600px]">
        <div className="flex-1 bg-white rounded-xl shadow-sm flex flex-col">
          <FileList />
        </div>
        <div className="w-full lg:w-80 flex flex-col gap-4 min-h-[600px]">
          <div className="space-y-4">
            <Button
              onClick={onUploadClick}
              className="w-full bg-custom-bg hover:bg-custom-bg-600 rounded-xl h-12 text-sm font-medium"
            >
              {t("uploadAudioVideo")}
            </Button>
            <Button
              onClick={onYouTubeClick}
              className="w-full bg-custom-bg hover:bg-custom-bg-600 rounded-xl h-12 text-sm font-medium"
            >
              {t("pasteYouTubeLink")}
            </Button>
            <RemainingMinutes />
          </div>
          <div className="flex-1 flex items-end">
            {shouldShowPromoCard && <DashboardPromotionCard />}
          </div>
        </div>
      </div>

      {/* Upload Dialogs */}
      <UploadDialog
        isOpen={showUpload}
        onOpenChange={setShowUpload}
        onUploadSuccess={handleUploadSuccess}
      />
      <YouTubeUploadDialog
        ref={youtubeDialogRef}
        onTranscribeSubmit={handleYouTubeSubmitSuccess}
      />

      {/* 限制提示弹窗 */}
      <LimitReachedDialog
        isOpen={showLimitDialog}
        onClose={() => setShowLimitDialog(false)}
        onUpgrade={handleUpgradeClick}
        source={upgradeSource}
        message={limitMessage}
      />

      {/* 升级弹窗 */}
      <UpgradeDialog
        isOpen={showUpgradeDialog}
        onClose={() => setShowUpgradeDialog(false)}
        source={upgradeSource}
      />

      {/* AppSumo 欢迎弹窗 */}
      <AppSumoWelcomeDialog
        isOpen={showAppSumoWelcomeDialog}
        onClose={() => setShowAppSumoWelcomeDialog(false)}
        user={user}
      />

      {/* AppSumo 激活错误弹窗 */}
      <AppSumoActivationErrorDialog
        isOpen={!!appsumoActivationError}
        onClose={clearAppSumoActivationError}
        errorCode={appsumoActivationError}
      />
    </div>
  );
}
